import { _decorator, Color, Component, Graphics, UITransform, view, Vec3, tween, Node } from 'cc';
import { GameConfig } from './GameConfig';
const { ccclass } = _decorator;

@ccclass('EnterRoomMask')
export class EnterRoomMask extends Component {
    private coverG: Graphics; // Cover节点的Graphics组件，用于黑色背景
    private targetPos: Vec3;
    private player: Node;
    private currentRadius: number;
    private animationDuration = 2.0; // 动画持续时间，调整为更合适的时长
    private finalRadius = 30; // 最终半径，可以调整
    private minPlayerScale = 0.3; // 玩家最小缩放比例，可以调整

    public start() {
        const winSize = view.getVisibleSize();

        // 使用Cover节点来绘制黑色背景
        const cover = this.node.getChildByName('Cover');
        this.coverG = cover.getComponent(Graphics);
        cover.getComponent(UITransform).setContentSize(winSize);

        // 设置层级
        this.node.getComponent(UITransform).priority = GameConfig.UIPriority.lampMask + 5;

        // 初始半径覆盖整个屏幕
        this.currentRadius = Math.max(winSize.width, winSize.height);

        console.log(`[ENTER_ROOM_MASK] start() 初始化完成，初始半径: ${this.currentRadius}`);
    }

    /**
     * 开始进入房间动画
     * @param player 玩家节点
     * @param entranceUIPos 入口在UI坐标系中的位置
     * @param onComplete 动画完成回调
     */
    public startEnterAnimation(player: Node, entranceUIPos: Vec3, onComplete: () => void) {
        this.player = player;
        this.targetPos = entranceUIPos;

        console.log(`[ENTER_ROOM_MASK] 开始进入房间动画，目标UI位置: (${this.targetPos.x}, ${this.targetPos.y})`);
        console.log(`[ENTER_ROOM_MASK] 动画持续时间: ${this.animationDuration}秒`);
        console.log(`[ENTER_ROOM_MASK] 当前半径: ${this.currentRadius}`);

        // 先进行一次初始绘制
        this.updateMask();

        // 立即开始动画，不再延迟
        this.startTweenAnimation(onComplete);
    }

    /**
     * 开始tween动画
     */
    private startTweenAnimation(onComplete: () => void) {
        console.log(`[ENTER_ROOM_MASK] 📍 startTweenAnimation 被调用`);

        // 开始缩小动画 - 使用自定义动画对象
        const animationTarget = { radius: this.currentRadius };

        console.log(`[ENTER_ROOM_MASK] 开始tween动画，初始半径: ${this.currentRadius}，目标半径: ${this.finalRadius}`);

        const tweenInstance = tween(animationTarget)
            .to(this.animationDuration, { radius: this.finalRadius }, {
                easing: 'quartOut', // 添加缓动效果
                onUpdate: () => {
                    this.currentRadius = animationTarget.radius;
                    this.updateMask();
                    this.updatePlayerScale();
                    // 每秒输出一次进度，添加安全检查
                    if (this.currentRadius !== undefined && Math.floor(Date.now() / 1000) % 1 === 0) {
                        console.log(`[ENTER_ROOM_MASK] 🎬 动画进行中，当前半径: ${this.currentRadius.toFixed(1)}`);
                    }
                },
                onComplete: () => {
                    console.log(`[ENTER_ROOM_MASK] ✅ 动画完成，当前半径: ${this.currentRadius}`);
                    console.log(`[ENTER_ROOM_MASK] 准备加载隐藏房间`);
                    onComplete();
                }
            });

        console.log(`[ENTER_ROOM_MASK] 🚀 tween实例创建完成，开始启动`);
        tweenInstance.start();
        console.log(`[ENTER_ROOM_MASK] 🎯 tween.start() 已调用`);
    }

    /**
     * 更新遮罩绘制
     */
    private updateMask() {
        if (!this.coverG) {
            console.warn(`[ENTER_ROOM_MASK] ⚠️ Graphics组件不存在，coverG=${!!this.coverG}`);
            return;
        }

        if (this.currentRadius === undefined || !this.targetPos) {
            console.warn(`[ENTER_ROOM_MASK] ⚠️ 参数不完整: radius=${this.currentRadius}, targetPos=${this.targetPos}`);
            return;
        }

        const winSize = view.getVisibleSize();

        // 方法：绘制四个矩形来形成遮罩，中间留出圆形洞
        this.coverG.clear();
        this.coverG.fillColor = new Color(0, 0, 0, 255); // 黑色

        const centerX = this.targetPos.x;
        const centerY = this.targetPos.y;
        const radius = this.currentRadius;

        // 计算圆形边界框
        const circleLeft = centerX - radius;
        const circleRight = centerX + radius;
        const circleTop = centerY + radius;
        const circleBottom = centerY - radius;

        // 屏幕边界
        const screenLeft = -winSize.width / 2;
        const screenRight = winSize.width / 2;
        const screenTop = winSize.height / 2;
        const screenBottom = -winSize.height / 2;

        // 绘制四个矩形遮罩
        // 左侧遮罩
        if (circleLeft > screenLeft) {
            this.coverG.fillRect(screenLeft, screenBottom, circleLeft - screenLeft, winSize.height);
        }

        // 右侧遮罩
        if (circleRight < screenRight) {
            this.coverG.fillRect(circleRight, screenBottom, screenRight - circleRight, winSize.height);
        }

        // 上方遮罩
        if (circleTop < screenTop) {
            this.coverG.fillRect(Math.max(screenLeft, circleLeft), circleTop,
                               Math.min(screenRight, circleRight) - Math.max(screenLeft, circleLeft), screenTop - circleTop);
        }

        // 下方遮罩
        if (circleBottom > screenBottom) {
            this.coverG.fillRect(Math.max(screenLeft, circleLeft), screenBottom,
                               Math.min(screenRight, circleRight) - Math.max(screenLeft, circleLeft), circleBottom - screenBottom);
        }

        console.log(`[ENTER_ROOM_MASK] 🎨 遮罩已更新，半径: ${this.currentRadius.toFixed(1)}, 位置: (${this.targetPos.x.toFixed(1)}, ${this.targetPos.y.toFixed(1)})`);
    }

    /**
     * 更新玩家缩放
     */
    private updatePlayerScale() {
        if (!this.player) return;
        if (this.currentRadius === undefined) return;

        // 根据遮罩半径计算玩家缩放比例
        const maxRadius = Math.max(view.getVisibleSize().width, view.getVisibleSize().height);
        const scaleRatio = Math.max(this.minPlayerScale, this.currentRadius / maxRadius); // 使用配置的最小缩放

        this.player.setScale(new Vec3(scaleRatio, scaleRatio, 1));

        // 玩家位置向目标位置移动
        if (this.currentRadius < maxRadius * 0.8) { // 当遮罩缩小到一定程度时开始移动玩家
            const currentPlayerPos = this.player.position;
            const moveRatio = 1 - (this.currentRadius / (maxRadius * 0.8));
            const targetX = currentPlayerPos.x + (this.targetPos.x - currentPlayerPos.x) * moveRatio * 0.1;
            const targetY = currentPlayerPos.y + (this.targetPos.y - currentPlayerPos.y) * moveRatio * 0.1;

            this.player.setPosition(new Vec3(targetX, targetY, currentPlayerPos.z));
        }
    }

    /**
     * 清理并销毁遮罩
     */
    public cleanupAndDestroy() {
        // 恢复玩家缩放
        if (this.player) {
            this.player.setScale(new Vec3(1, 1, 1));
        }

        this.node.destroy();
    }
}
